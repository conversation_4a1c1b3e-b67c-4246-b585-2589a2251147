import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface CustomizationConfig {
  exterior: {
    bodyColor: {
      primary: string;
      secondary?: string;
      finish: 'matte' | 'gloss' | 'metallic' | 'pearl' | 'chrome';
    };
    bodyKit: {
      front?: string;
      rear?: string;
      side?: string;
      spoiler?: string;
    };
    wheels: {
      brand?: string;
      model?: string;
      size?: string;
      color?: string;
      finish?: string;
    };
    lighting: {
      headlights?: string;
      taillights?: string;
      underglow?: string;
      interior?: string;
    };
    graphics: {
      decals?: string[];
      wraps?: string;
      customText?: string;
    };
  };
  interior: {
    seats: {
      material?: string;
      color?: string;
      pattern?: string;
      stitching?: string;
    };
    dashboard: {
      material?: string;
      color?: string;
      trim?: string;
    };
    steeringWheel: {
      material?: string;
      color?: string;
      style?: string;
    };
    floorMats: {
      material?: string;
      color?: string;
      logo?: boolean;
    };
    lighting: {
      ambient?: string;
      footwell?: string;
      dashboard?: string;
    };
  };
  performance: {
    engine: {
      tuning?: string;
      airIntake?: string;
      exhaust?: string;
      turbo?: string;
    };
    suspension: {
      type?: string;
      height?: string;
      stiffness?: string;
    };
    brakes: {
      front?: string;
      rear?: string;
      calipers?: string;
    };
    transmission: {
      type?: string;
      modifications?: string[];
    };
  };
  audio: {
    headUnit?: string;
    speakers: {
      front?: string;
      rear?: string;
      subwoofer?: string;
    };
    amplifier?: string;
    soundDeadening?: boolean;
  };
}

interface CustomizationState {
  currentCustomization: {
    id?: string;
    carModelId?: string;
    name?: string;
    description?: string;
    configuration: CustomizationConfig;
    estimatedCost: {
      parts: number;
      labor: number;
      total: number;
    };
    tags: string[];
    isPublic: boolean;
    isSaved: boolean;
  };
  selectedServices: Array<{
    serviceId: string;
    serviceName: string;
    basePrice: number;
    customizations: Array<{
      optionName: string;
      selectedValue: string;
      additionalCost: number;
    }>;
    quantity: number;
    totalPrice: number;
  }>;
  isCustomizing: boolean;
  activeCategory: 'exterior' | 'interior' | 'performance' | 'audio';
  previewMode: '3d' | '2d' | 'ar';
}

interface CustomizationActions {
  setCarModel: (carModelId: string) => void;
  updateConfiguration: (category: keyof CustomizationConfig, updates: any) => void;
  addService: (service: {
    serviceId: string;
    serviceName: string;
    basePrice: number;
    customizations?: Array<{
      optionName: string;
      selectedValue: string;
      additionalCost: number;
    }>;
    quantity?: number;
  }) => void;
  removeService: (serviceId: string) => void;
  updateServiceCustomization: (serviceId: string, customizations: any[]) => void;
  setActiveCategory: (category: 'exterior' | 'interior' | 'performance' | 'audio') => void;
  setPreviewMode: (mode: '3d' | '2d' | 'ar') => void;
  calculateEstimatedCost: () => void;
  saveCustomization: (name: string, description?: string) => void;
  loadCustomization: (customization: any) => void;
  resetCustomization: () => void;
  setCustomizationName: (name: string) => void;
  setCustomizationDescription: (description: string) => void;
  togglePublic: () => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
}

type CustomizationStore = CustomizationState & CustomizationActions;

const initialConfiguration: CustomizationConfig = {
  exterior: {
    bodyColor: {
      primary: '#FFFFFF',
      finish: 'gloss'
    },
    bodyKit: {},
    wheels: {},
    lighting: {},
    graphics: {}
  },
  interior: {
    seats: {},
    dashboard: {},
    steeringWheel: {},
    floorMats: {},
    lighting: {}
  },
  performance: {
    engine: {},
    suspension: {},
    brakes: {},
    transmission: {}
  },
  audio: {
    speakers: {}
  }
};

export const useCustomizationStore = create<CustomizationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentCustomization: {
        configuration: initialConfiguration,
        estimatedCost: {
          parts: 0,
          labor: 0,
          total: 0
        },
        tags: [],
        isPublic: false,
        isSaved: false
      },
      selectedServices: [],
      isCustomizing: false,
      activeCategory: 'exterior',
      previewMode: '3d',

      // Actions
      setCarModel: (carModelId: string) => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            carModelId,
            isSaved: false
          },
          isCustomizing: true
        }));
      },

      updateConfiguration: (category, updates) => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            configuration: {
              ...state.currentCustomization.configuration,
              [category]: {
                ...state.currentCustomization.configuration[category],
                ...updates
              }
            },
            isSaved: false
          }
        }));
        
        // Recalculate cost after configuration update
        get().calculateEstimatedCost();
      },

      addService: (service) => {
        set((state) => {
          const existingServiceIndex = state.selectedServices.findIndex(
            s => s.serviceId === service.serviceId
          );

          let updatedServices;
          if (existingServiceIndex >= 0) {
            // Update existing service
            updatedServices = [...state.selectedServices];
            updatedServices[existingServiceIndex] = {
              ...service,
              quantity: service.quantity || 1,
              totalPrice: service.basePrice * (service.quantity || 1)
            };
          } else {
            // Add new service
            updatedServices = [
              ...state.selectedServices,
              {
                ...service,
                customizations: service.customizations || [],
                quantity: service.quantity || 1,
                totalPrice: service.basePrice * (service.quantity || 1)
              }
            ];
          }

          return {
            selectedServices: updatedServices,
            currentCustomization: {
              ...state.currentCustomization,
              isSaved: false
            }
          };
        });

        get().calculateEstimatedCost();
      },

      removeService: (serviceId) => {
        set((state) => ({
          selectedServices: state.selectedServices.filter(s => s.serviceId !== serviceId),
          currentCustomization: {
            ...state.currentCustomization,
            isSaved: false
          }
        }));

        get().calculateEstimatedCost();
      },

      updateServiceCustomization: (serviceId, customizations) => {
        set((state) => {
          const updatedServices = state.selectedServices.map(service => {
            if (service.serviceId === serviceId) {
              const additionalCost = customizations.reduce((sum, c) => sum + (c.additionalCost || 0), 0);
              return {
                ...service,
                customizations,
                totalPrice: (service.basePrice + additionalCost) * service.quantity
              };
            }
            return service;
          });

          return {
            selectedServices: updatedServices,
            currentCustomization: {
              ...state.currentCustomization,
              isSaved: false
            }
          };
        });

        get().calculateEstimatedCost();
      },

      setActiveCategory: (category) => {
        set({ activeCategory: category });
      },

      setPreviewMode: (mode) => {
        set({ previewMode: mode });
      },

      calculateEstimatedCost: () => {
        const { selectedServices } = get();
        
        const parts = selectedServices.reduce((sum, service) => sum + service.totalPrice, 0);
        const labor = Math.round(parts * 0.3); // 30% of parts cost
        const total = parts + labor;

        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            estimatedCost: { parts, labor, total }
          }
        }));
      },

      saveCustomization: (name, description) => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            name,
            description,
            isSaved: true
          }
        }));
      },

      loadCustomization: (customization) => {
        set({
          currentCustomization: customization,
          selectedServices: [], // Reset services when loading
          isCustomizing: true
        });
      },

      resetCustomization: () => {
        set({
          currentCustomization: {
            configuration: initialConfiguration,
            estimatedCost: { parts: 0, labor: 0, total: 0 },
            tags: [],
            isPublic: false,
            isSaved: false
          },
          selectedServices: [],
          isCustomizing: false,
          activeCategory: 'exterior'
        });
      },

      setCustomizationName: (name) => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            name,
            isSaved: false
          }
        }));
      },

      setCustomizationDescription: (description) => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            description,
            isSaved: false
          }
        }));
      },

      togglePublic: () => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            isPublic: !state.currentCustomization.isPublic,
            isSaved: false
          }
        }));
      },

      addTag: (tag) => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            tags: [...state.currentCustomization.tags, tag],
            isSaved: false
          }
        }));
      },

      removeTag: (tag) => {
        set((state) => ({
          currentCustomization: {
            ...state.currentCustomization,
            tags: state.currentCustomization.tags.filter(t => t !== tag),
            isSaved: false
          }
        }));
      }
    }),
    {
      name: 'customization-storage',
      partialize: (state) => ({
        currentCustomization: state.currentCustomization,
        selectedServices: state.selectedServices
      })
    }
  )
);
