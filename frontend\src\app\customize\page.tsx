'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Car, Palette, Settings, Zap, Volume2, ArrowRight } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useCustomizationStore } from '@/store/customizationStore';
import { carsAPI } from '@/lib/api';
import toast from 'react-hot-toast';

interface CarModel {
  _id: string;
  make: string;
  model: string;
  year: number;
  images: {
    main: string;
  };
}

const CustomizePage: React.FC = () => {
  const searchParams = useSearchParams();
  const carId = searchParams.get('car');
  
  const [selectedCar, setSelectedCar] = useState<CarModel | null>(null);
  const [cars, setCars] = useState<CarModel[]>([]);
  const [loading, setLoading] = useState(true);
  
  const { 
    activeCategory, 
    setActiveCategory, 
    setCarModel, 
    currentCustomization,
    resetCustomization 
  } = useCustomizationStore();

  const categories = [
    {
      id: 'exterior',
      name: 'Exterior',
      icon: Car,
      description: 'Body kits, paint, wheels, and lighting',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'interior',
      name: 'Interior',
      icon: Palette,
      description: 'Seats, dashboard, and cabin styling',
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'performance',
      name: 'Performance',
      icon: Zap,
      description: 'Engine, exhaust, and suspension',
      color: 'from-red-500 to-red-600'
    },
    {
      id: 'audio',
      name: 'Audio',
      icon: Volume2,
      description: 'Sound system and entertainment',
      color: 'from-green-500 to-green-600'
    }
  ];

  useEffect(() => {
    fetchCars();
    if (carId) {
      fetchCarDetails(carId);
    }
  }, [carId]);

  const fetchCars = async () => {
    try {
      const response = await carsAPI.getCars({ limit: 20 });
      if (response.success) {
        setCars(response.data.cars);
      }
    } catch (error) {
      console.error('Error fetching cars:', error);
      toast.error('Failed to fetch cars');
    } finally {
      setLoading(false);
    }
  };

  const fetchCarDetails = async (id: string) => {
    try {
      const response = await carsAPI.getCarById(id);
      if (response.success) {
        setSelectedCar(response.data.car);
        setCarModel(id);
      }
    } catch (error) {
      console.error('Error fetching car details:', error);
      toast.error('Failed to fetch car details');
    }
  };

  const handleCarSelect = (car: CarModel) => {
    setSelectedCar(car);
    setCarModel(car._id);
    toast.success(`Selected ${car.make} ${car.model} for customization`);
  };

  const handleCategorySelect = (categoryId: string) => {
    setActiveCategory(categoryId as any);
    toast.info(`Switched to ${categoryId} customization`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-white">Loading customization studio...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Customization <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-600">Studio</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Design your perfect vehicle with our advanced customization tools
          </p>
        </div>

        {!selectedCar ? (
          /* Car Selection */
          <div>
            <h2 className="text-2xl font-bold text-white mb-6">Choose Your Vehicle</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cars.map((car, index) => (
                <motion.div
                  key={car._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card 
                    variant="glass" 
                    hover 
                    className="cursor-pointer"
                    onClick={() => handleCarSelect(car)}
                  >
                    <div className="relative h-48 overflow-hidden rounded-t-xl">
                      <img
                        src={car.images.main}
                        alt={`${car.make} ${car.model}`}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <CardContent className="p-4">
                      <h3 className="text-lg font-semibold text-white">
                        {car.make} {car.model}
                      </h3>
                      <p className="text-gray-400">{car.year}</p>
                      <Button variant="primary" size="sm" className="mt-3" fullWidth>
                        Customize This Car
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        ) : (
          /* Customization Interface */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Car Preview */}
            <div className="lg:col-span-2">
              <Card variant="glass">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-xl font-semibold text-white">
                        {selectedCar.make} {selectedCar.model} {selectedCar.year}
                      </h3>
                      <p className="text-gray-400">3D Preview</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedCar(null);
                        resetCustomization();
                      }}
                    >
                      Change Car
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="relative h-96 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg flex items-center justify-center">
                    <img
                      src={selectedCar.images.main}
                      alt={`${selectedCar.make} ${selectedCar.model}`}
                      className="max-h-full max-w-full object-contain"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                      <div className="text-center text-white">
                        <Settings className="h-12 w-12 mx-auto mb-4 animate-spin" />
                        <p className="text-lg font-semibold">3D Viewer Loading...</p>
                        <p className="text-sm text-gray-300">Advanced 3D customization coming soon</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Customization Categories */}
            <div>
              <Card variant="glass">
                <CardHeader>
                  <h3 className="text-xl font-semibold text-white">Customization Categories</h3>
                </CardHeader>
                <CardContent className="space-y-4">
                  {categories.map((category) => {
                    const IconComponent = category.icon;
                    const isActive = activeCategory === category.id;
                    
                    return (
                      <motion.div
                        key={category.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div
                          className={`p-4 rounded-lg cursor-pointer transition-all duration-200 ${
                            isActive
                              ? `bg-gradient-to-r ${category.color} text-white shadow-lg`
                              : 'bg-white/5 hover:bg-white/10 text-gray-300'
                          }`}
                          onClick={() => handleCategorySelect(category.id)}
                        >
                          <div className="flex items-center space-x-3">
                            <IconComponent className="h-6 w-6" />
                            <div className="flex-1">
                              <h4 className="font-semibold">{category.name}</h4>
                              <p className="text-sm opacity-80">{category.description}</p>
                            </div>
                            <ArrowRight className="h-4 w-4" />
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </CardContent>
              </Card>

              {/* Customization Summary */}
              <Card variant="glass" className="mt-6">
                <CardHeader>
                  <h3 className="text-xl font-semibold text-white">Customization Summary</h3>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Parts Cost:</span>
                      <span className="text-white">₹{currentCustomization.estimatedCost.parts.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Labor Cost:</span>
                      <span className="text-white">₹{currentCustomization.estimatedCost.labor.toLocaleString()}</span>
                    </div>
                    <hr className="border-gray-600" />
                    <div className="flex justify-between font-semibold">
                      <span className="text-white">Total:</span>
                      <span className="text-orange-400">₹{currentCustomization.estimatedCost.total.toLocaleString()}</span>
                    </div>
                  </div>
                  
                  <div className="mt-6 space-y-2">
                    <Button variant="primary" fullWidth>
                      Save Customization
                    </Button>
                    <Button variant="outline" fullWidth>
                      Book Appointment
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomizePage;
