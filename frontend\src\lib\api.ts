import axios from 'axios';
import Cookies from 'js-cookie';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth token and redirect to login
      Cookies.remove('auth_token');
      window.location.href = '/auth/login';
    }
    
    return Promise.reject(error.response?.data || error.message);
  }
);

// Auth API
export const authAPI = {
  register: (data: {
    name: string;
    email: string;
    password: string;
    phone?: string;
  }) => api.post('/auth/register', data),

  login: (data: { email: string; password: string }) =>
    api.post('/auth/login', data),

  getProfile: () => api.get('/auth/me'),

  updateProfile: (data: {
    name?: string;
    phone?: string;
    preferences?: any;
  }) => api.put('/auth/profile', data),
};

// Cars API
export const carsAPI = {
  getCars: (params?: {
    page?: number;
    limit?: number;
    make?: string;
    category?: string;
    minPrice?: number;
    maxPrice?: number;
    year?: number;
    featured?: boolean;
    search?: string;
  }) => api.get('/cars', { params }),

  getFeaturedCars: () => api.get('/cars/featured'),

  getCarMakes: () => api.get('/cars/makes'),

  getCarById: (id: string) => api.get(`/cars/${id}`),
};

// Services API
export const servicesAPI = {
  getServices: (params?: {
    page?: number;
    limit?: number;
    category?: string;
    subcategory?: string;
    minPrice?: number;
    maxPrice?: number;
    popular?: boolean;
    search?: string;
    tags?: string;
  }) => api.get('/services', { params }),

  getServiceCategories: () => api.get('/services/categories'),

  getPopularServices: () => api.get('/services/popular'),

  getServiceById: (id: string) => api.get(`/services/${id}`),
};

// Bookings API
export const bookingsAPI = {
  createBooking: (data: {
    carModel: string;
    services: Array<{
      service: string;
      customizations?: Array<{
        optionName: string;
        selectedValue: string;
        additionalCost?: number;
      }>;
      quantity?: number;
      notes?: string;
    }>;
    schedule: {
      preferredDate: string;
      preferredTime: string;
    };
    contact: {
      phone: string;
      email?: string;
      address?: {
        street?: string;
        city?: string;
        state?: string;
        pincode?: string;
      };
    };
    customerCar: {
      make: string;
      model: string;
      year: number;
      color?: string;
      registrationNumber?: string;
      mileage?: number;
    };
    notes?: string;
  }) => api.post('/bookings', data),

  getBookings: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
  }) => api.get('/bookings', { params }),

  getBookingById: (id: string) => api.get(`/bookings/${id}`),

  updateBookingStatus: (id: string, data: { status: string; notes?: string }) =>
    api.put(`/bookings/${id}/status`, data),
};

// Customizations API
export const customizationsAPI = {
  createCustomization: (data: {
    carModel: string;
    name: string;
    description?: string;
    configuration: any;
    tags?: string[];
    isPublic?: boolean;
  }) => api.post('/customizations', data),

  getCustomizations: (params?: {
    page?: number;
    limit?: number;
    carModel?: string;
    public?: boolean;
    search?: string;
    tags?: string;
    my?: boolean;
  }) => api.get('/customizations', { params }),

  getCustomizationById: (id: string) => api.get(`/customizations/${id}`),

  updateCustomization: (id: string, data: any) =>
    api.put(`/customizations/${id}`, data),

  likeCustomization: (id: string) => api.post(`/customizations/${id}/like`),

  deleteCustomization: (id: string) => api.delete(`/customizations/${id}`),
};

// Upload API
export const uploadAPI = {
  uploadImage: (folder: string, file: File) => {
    const formData = new FormData();
    formData.append('image', file);
    return api.post(`/upload/${folder}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  uploadMultipleImages: (folder: string, files: File[]) => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('images', file);
    });
    return api.post(`/upload/${folder}/multiple`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  deleteImage: (folder: string, filename: string) =>
    api.delete(`/upload/${folder}/${filename}`),
};

export default api;
