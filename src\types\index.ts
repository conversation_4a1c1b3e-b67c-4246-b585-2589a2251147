export interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface CarModel {
  id: string
  make: string
  model: string
  year: number
  image_url?: string
}

export interface ModificationCategory {
  id: string
  name: string
  description: string
  icon: string
  image_url?: string
}

export interface ModificationService {
  id: string
  category_id: string
  name: string
  description: string
  price: number
  duration_hours: number
  image_url?: string
  features: string[]
  is_popular: boolean
}

export interface CustomizationOption {
  id: string
  service_id: string
  name: string
  type: 'color' | 'material' | 'size' | 'style'
  options: string[]
  additional_cost: number
}

export interface Booking {
  id: string
  user_id: string
  car_model_id: string
  services: BookingService[]
  total_amount: number
  booking_date: string
  preferred_time: string
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'
  notes?: string
  created_at: string
  updated_at: string
}

export interface BookingService {
  service_id: string
  customizations: Record<string, string>
  price: number
}

export interface CarCustomization {
  id: string
  user_id: string
  car_model_id: string
  name: string
  configuration: CustomizationConfig
  preview_image_url?: string
  is_saved: boolean
  created_at: string
  updated_at: string
}

export interface CustomizationConfig {
  exterior: {
    color: string
    wheels: string
    body_kit: string
    spoiler: string
    lights: string
  }
  interior: {
    seats: string
    dashboard: string
    steering_wheel: string
    lighting: string
  }
  performance: {
    engine: string
    exhaust: string
    suspension: string
    brakes: string
  }
}

export interface Review {
  id: string
  user_id: string
  service_id: string
  rating: number
  comment: string
  images?: string[]
  created_at: string
}

export interface Gallery {
  id: string
  title: string
  description: string
  images: string[]
  category: string
  featured: boolean
  created_at: string
}
