'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Heart, Eye, Share2, Car } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const GalleryPage: React.FC = () => {
  const galleryItems = [
    {
      id: 1,
      title: 'Aggressive Swift Transformation',
      category: 'Exterior',
      image: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=800',
      likes: 245,
      views: 1200,
      description: 'Complete body kit with custom paint and performance upgrades'
    },
    {
      id: 2,
      title: 'Luxury Creta Interior',
      category: 'Interior',
      image: 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=800',
      likes: 189,
      views: 890,
      description: 'Premium leather upholstery with ambient lighting'
    },
    {
      id: 3,
      title: 'Performance Thar Build',
      category: 'Performance',
      image: 'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=800',
      likes: 312,
      views: 1500,
      description: 'Lifted suspension with off-road performance mods'
    },
    {
      id: 4,
      title: 'Custom LED Setup',
      category: 'Lighting',
      image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800',
      likes: 156,
      views: 670,
      description: 'Full LED conversion with custom DRL patterns'
    },
    {
      id: 5,
      title: 'Racing Stripes Design',
      category: 'Exterior',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800',
      likes: 203,
      views: 980,
      description: 'Classic racing stripes with modern twist'
    },
    {
      id: 6,
      title: 'Audio System Upgrade',
      category: 'Audio',
      image: 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=800',
      likes: 134,
      views: 560,
      description: 'Premium sound system with custom installation'
    }
  ];

  const categories = ['All', 'Exterior', 'Interior', 'Performance', 'Lighting', 'Audio'];
  const [selectedCategory, setSelectedCategory] = React.useState('All');

  const filteredItems = selectedCategory === 'All' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === selectedCategory);

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Customization <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-600">Gallery</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Explore our portfolio of stunning car modifications and get inspired for your next project
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-8">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className={selectedCategory === category ? '' : 'text-white border-white/20 hover:bg-white/10'}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card variant="glass" hover className="overflow-hidden group">
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  
                  {/* Category Badge */}
                  <div className="absolute top-3 left-3 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                    {item.category}
                  </div>

                  {/* Hover Actions */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="flex space-x-2">
                      <Button variant="primary" size="sm" className="bg-white/20 backdrop-blur-sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="primary" size="sm" className="bg-white/20 backdrop-blur-sm">
                        <Heart className="h-4 w-4" />
                      </Button>
                      <Button variant="primary" size="sm" className="bg-white/20 backdrop-blur-sm">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <CardContent className="p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-400 text-sm mb-3">
                    {item.description}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-300">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Heart className="h-4 w-4" />
                        <span>{item.likes}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{item.views}</span>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="text-orange-400 hover:text-orange-300">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <Card variant="glass" className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <Car className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-4">
                Ready to Create Your Own Masterpiece?
              </h3>
              <p className="text-gray-300 mb-6">
                Start customizing your vehicle today and join our gallery of satisfied customers
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="primary" size="lg">
                  Start Customizing
                </Button>
                <Button variant="outline" size="lg" className="text-white border-white/20 hover:bg-white/10">
                  Book Consultation
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default GalleryPage;
