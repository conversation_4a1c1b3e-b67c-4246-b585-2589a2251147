import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          phone?: string
          avatar_url?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          phone?: string
          avatar_url?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          phone?: string
          avatar_url?: string
          updated_at?: string
        }
      }
      car_models: {
        Row: {
          id: string
          make: string
          model: string
          year: number
          image_url?: string
          base_price: number
          created_at: string
        }
        Insert: {
          id?: string
          make: string
          model: string
          year: number
          image_url?: string
          base_price: number
          created_at?: string
        }
        Update: {
          id?: string
          make?: string
          model?: string
          year?: number
          image_url?: string
          base_price?: number
        }
      }
      modification_categories: {
        Row: {
          id: string
          name: string
          description: string
          icon: string
          image_url?: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          icon: string
          image_url?: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          icon?: string
          image_url?: string
        }
      }
      modification_services: {
        Row: {
          id: string
          category_id: string
          name: string
          description: string
          price: number
          duration_hours: number
          image_url?: string
          features: string[]
          is_popular: boolean
          created_at: string
        }
        Insert: {
          id?: string
          category_id: string
          name: string
          description: string
          price: number
          duration_hours: number
          image_url?: string
          features: string[]
          is_popular?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          category_id?: string
          name?: string
          description?: string
          price?: number
          duration_hours?: number
          image_url?: string
          features?: string[]
          is_popular?: boolean
        }
      }
      bookings: {
        Row: {
          id: string
          user_id: string
          car_model_id: string
          services: any
          total_amount: number
          booking_date: string
          preferred_time: string
          status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'
          notes?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          car_model_id: string
          services: any
          total_amount: number
          booking_date: string
          preferred_time: string
          status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'
          notes?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          car_model_id?: string
          services?: any
          total_amount?: number
          booking_date?: string
          preferred_time?: string
          status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'
          notes?: string
          updated_at?: string
        }
      }
      car_customizations: {
        Row: {
          id: string
          user_id: string
          car_model_id: string
          name: string
          configuration: any
          preview_image_url?: string
          is_saved: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          car_model_id: string
          name: string
          configuration: any
          preview_image_url?: string
          is_saved?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          car_model_id?: string
          name?: string
          configuration?: any
          preview_image_url?: string
          is_saved?: boolean
          updated_at?: string
        }
      }
    }
  }
}
