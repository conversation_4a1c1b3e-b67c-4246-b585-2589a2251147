'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Search, Filter, Car, Star, ArrowRight } from 'lucide-react';
import { Card, CardContent, CardFooter } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { carsAPI } from '@/lib/api';
import { formatCurrency } from '@/lib/utils';
import toast from 'react-hot-toast';

interface CarModel {
  _id: string;
  make: string;
  model: string;
  year: number;
  category: string;
  basePrice: number;
  images: {
    main: string;
    gallery?: string[];
  };
  specifications: {
    engine: string;
    transmission: string;
    fuelType: string;
    seatingCapacity: number;
  };
  featured: boolean;
}

const CarsPage: React.FC = () => {
  const [cars, setCars] = useState<CarModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedMake, setSelectedMake] = useState('');
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [makes, setMakes] = useState<string[]>([]);

  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'sedan', label: 'Sedan' },
    { value: 'suv', label: 'SUV' },
    { value: 'hatchback', label: 'Hatchback' },
    { value: 'coupe', label: 'Coupe' },
    { value: 'sports', label: 'Sports' },
  ];

  useEffect(() => {
    fetchCars();
    fetchMakes();
  }, []);

  useEffect(() => {
    fetchCars();
  }, [searchTerm, selectedCategory, selectedMake, priceRange]);

  const fetchCars = async () => {
    try {
      setLoading(true);
      const params: any = {};
      
      if (searchTerm) params.search = searchTerm;
      if (selectedCategory) params.category = selectedCategory;
      if (selectedMake) params.make = selectedMake;
      if (priceRange.min) params.minPrice = parseInt(priceRange.min);
      if (priceRange.max) params.maxPrice = parseInt(priceRange.max);

      const response = await carsAPI.getCars(params);
      
      if (response.success) {
        setCars(response.data.cars);
      } else {
        toast.error('Failed to fetch cars');
      }
    } catch (error) {
      console.error('Error fetching cars:', error);
      toast.error('Failed to fetch cars');
    } finally {
      setLoading(false);
    }
  };

  const fetchMakes = async () => {
    try {
      const response = await carsAPI.getCarMakes();
      if (response.success) {
        setMakes(response.data.makes);
      }
    } catch (error) {
      console.error('Error fetching makes:', error);
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedMake('');
    setPriceRange({ min: '', max: '' });
  };

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Choose Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-600">Dream Car</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Browse our collection of vehicles and start customizing your perfect ride
          </p>
        </div>

        {/* Filters */}
        <div className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-xl p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Input
              placeholder="Search cars..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
              className="bg-white/10 border-white/20 text-white placeholder-gray-400"
            />
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              {categories.map((category) => (
                <option key={category.value} value={category.value} className="bg-gray-800">
                  {category.label}
                </option>
              ))}
            </select>

            <select
              value={selectedMake}
              onChange={(e) => setSelectedMake(e.target.value)}
              className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="" className="bg-gray-800">All Makes</option>
              {makes.map((make) => (
                <option key={make} value={make} className="bg-gray-800">
                  {make}
                </option>
              ))}
            </select>

            <Input
              placeholder="Min Price"
              type="number"
              value={priceRange.min}
              onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
              className="bg-white/10 border-white/20 text-white placeholder-gray-400"
            />

            <Input
              placeholder="Max Price"
              type="number"
              value={priceRange.max}
              onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
              className="bg-white/10 border-white/20 text-white placeholder-gray-400"
            />
          </div>

          <div className="flex justify-between items-center mt-4">
            <p className="text-gray-300">
              {cars.length} car{cars.length !== 1 ? 's' : ''} found
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-orange-400 hover:text-orange-300"
            >
              Clear Filters
            </Button>
          </div>
        </div>

        {/* Cars Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <Card key={index} variant="glass" className="animate-pulse">
                <div className="h-48 bg-gray-300 rounded-t-xl"></div>
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded w-2/3 mb-4"></div>
                  <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : cars.length === 0 ? (
          <div className="text-center py-12">
            <Car className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No cars found</h3>
            <p className="text-gray-400">Try adjusting your filters to see more results</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {cars.map((car, index) => (
              <motion.div
                key={car._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card variant="glass" hover className="overflow-hidden group">
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={car.images.main}
                      alt={`${car.make} ${car.model}`}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    {car.featured && (
                      <div className="absolute top-3 left-3 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        Featured
                      </div>
                    )}
                  </div>

                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="text-lg font-semibold text-white">
                          {car.make} {car.model}
                        </h3>
                        <p className="text-gray-400 text-sm">
                          {car.year} • {car.category.charAt(0).toUpperCase() + car.category.slice(1)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-orange-400 font-bold text-lg">
                          {formatCurrency(car.basePrice)}
                        </p>
                        <p className="text-gray-400 text-xs">Starting from</p>
                      </div>
                    </div>

                    <div className="space-y-1 text-sm text-gray-300">
                      <p>Engine: {car.specifications.engine}</p>
                      <p>Fuel: {car.specifications.fuelType}</p>
                      <p>Seats: {car.specifications.seatingCapacity}</p>
                    </div>
                  </CardContent>

                  <CardFooter className="p-4 pt-0">
                    <div className="flex space-x-2 w-full">
                      <Link href={`/cars/${car._id}`} className="flex-1">
                        <Button variant="outline" fullWidth size="sm">
                          View Details
                        </Button>
                      </Link>
                      <Link href={`/customize?car=${car._id}`} className="flex-1">
                        <Button variant="primary" fullWidth size="sm">
                          Customize
                          <ArrowRight className="h-4 w-4 ml-1" />
                        </Button>
                      </Link>
                    </div>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default CarsPage;
